
Integration
Usually, you want to integrate Leaflet Routing Machine’s functionality with other functions in your page or app. This tutorial will show you some common use cases and illustrates the integration points that are available in Leaflet Routing Machine.

Events
Like many other JavaScript libraries in general, and Leaflet and its plugins in particular, events are an important mechanism for integration and adding functionality.

Most parts of Leaflet Routing Machine will fire events that your code can listen for, and take action. This uses Leaflet’s event system, so if you are not familiar with it, now might be a good time to read up on it.

All events of the different components in Leaflet Routing Machine are documented in the Leaflet Routing Machine API docs.

Handling Routes - taking action on new routes
A common scenario is that your app wants to do something once a route has been calculated and displayed in Leaflet Routing Machine.

JavaScript and Leaflet Routing Machine are asynchronous: when a request for a route is sent, the code doesn’t halt to wait for the response, but continues immediately. The route will be available at some later point. Because of this, there’s no function like getRoute() or similar in Leaflet Routing Machine: if you need to access a route, you should instead listen for events, that will tell you when routes have been received from the routing backend, or when a route is displayed in the map.

The control will fire a routesfound event once the backend returns one or more route as a response to a routing request.

L.Routing.control({
        waypoints: [
            <PERSON><PERSON>lat<PERSON>(57.74, 11.94),
            <PERSON><PERSON>lat<PERSON>(57.6792, 11.949)
        ],
        routeWhileDragging: true,
        geocoder: L.Control.Geocoder.nominatim()
    })
    .on('routesfound', function(e) {
        var routes = e.routes;
        alert('Found ' + routes.length + ' route(s).');
    })
    .addTo(map); 
 
 
 
Similarily, and perhaps more common, is to take some action once a route is shown in the map and itinerary. The event routeselected is fired when a response is shown to the user, as well as when the user selects an alternative route from the control.

L.Routing.control({
        waypoints: [
            L.latLng(57.74, 11.94),
            L.latLng(57.6792, 11.949)
        ],
        routeWhileDragging: true,
        geocoder: L.Control.Geocoder.nominatim()
    })
    .on('routeselected', function(e) {
        var route = e.route;
        alert('Showing route between waypoints:\n' + JSON.stringify(route.inputWaypoints, null, 2));
    })
    .addTo(map); 
 
 
 
Try the (somewhat annoying) result below, drag the waypoints, alerts will popup as new routes are returned and selected:

+
−
Hjalmar Brantingsgatan, Linnégatan
9.3 km, 15 min
Head northeast	25 m
Turn left onto Bäcktuvevägen	150 m
Turn right to stay on Bäcktuvevägen	15 m
Enter the traffic circle and take the 1st exit onto Tuvevägen (O 570)	10 m
Exit the traffic circle onto Tuvevägen (O 570)	800 m
Enter the traffic circle and take the 2nd exit onto Tuvevägen	25 m
Exit the traffic circle onto Tuvevägen	250 m
Enter the traffic circle and take the 2nd exit onto Tuvevägen	35 m
Exit the traffic circle onto Tuvevägen	250 m
Enter the traffic circle and take the 1st exit onto Tuvevägen	25 m
Exit the traffic circle onto Tuvevägen	300 m
Enter the traffic circle and take the 2nd exit onto Gustaf Dalénsgatan	30 m
Exit the traffic circle onto Gustaf Dalénsgatan	800 m
Turn left towards Centrum	600 m
Keep right onto Hjalmar Brantingsgatan	200 m
Make a slight right to stay on Hjalmar Brantingsgatan	20 m
Keep left onto Hjalmar Brantingsgatan	450 m
Continue onto Hisingsbron	700 m
Turn right onto Norra Sjöfarten	150 m
Take the ramp on the left towards 158: Sahlgrenska	150 m
Merge left onto Götatunneln (E 45)	1.5 km
Take the ramp towards Centrum Syd	200 m
Turn left onto Järnvågsgatan	150 m
Keep right onto Järnvågsgatan	150 m
Continue onto Linnégatan	1 km
Continue onto Linnéplatsen	150 m
Enter Linnéplatsen and take the 1st exit onto Dag Hammarskjöldsleden	10 m
Exit the traffic circle onto Dag Hammarskjöldsleden	900 m
Make a slight right towards Högsbohöjd	300 m
Turn left onto Margretebergsgatan	50 m
Continue onto Storängsgatan	200 m
Turn right onto Fyradalersgatan	8 m
Turn left onto Storängsgatan	35 m
You have arrived at your destination	0 m
 Leaflet | Maps and routes from OpenStreetMap. data uses ODbL license
See RoutingResultEvent and RouteSelectedEvent for more details on data available from these events.

Spinner - indicate routes are calculated
Depending on the load on your backend, and the users network bandwidth, a routing request can be very quick (which is usually the case with OSRM), or take a while. Displaying some kind of feedback that a request is in progress can be a good idea.

To help with this, Leaflet Routing Machine fires a routingstart every time a routing request is sent to the backend. Corresponding to this, a routesfound (as shown above) or routingerror will fire to indicate success or failure. This can be used to display and hide a spinner:

L.Routing.control({
        waypoints: [
            L.latLng(57.74, 11.94),
            L.latLng(57.6792, 11.949)
        ],
        routeWhileDragging: true,
        geocoder: L.Control.Geocoder.nominatim()
    })
    .on('routingstart', showSpinner)
    .on('routesfound routingerror', hideSpinner)
    .addTo(map); 
 
Errors
As can be seen in the example above, Leaflet Routing Machine will fire a routingerror event if an error occurs during routing. By default, the control will listen for this event and log any errors to the console, but in a more complex application, you probably want to do some more advanced error handling to show the result to the user.

A quick way to add some basic error feedback is to use the built-in ErrorControl. It expects you to pass a L.Routing.Control to it, and will hook up to the error event:

var control = L.Routing.control({
        waypoints: [
            L.latLng(57.74, 11.94),
            L.latLng(57.6792, 11.949)
        ],
        routeWhileDragging: true,
        geocoder: L.Control.Geocoder.nominatim()
    });

L.Routing.errorControl(control).addTo(map);
 
The default console error handling can be disabled by passing the option defaultErrorHandler to false.

Copyright © 2015 Per Lied


Addresses and geocoders
Routing and addresses are tightly coupled. Perhaps the most common use case for routing if to get from address A to address B, where the user does not necessarily know the geographic location of those addresses. Since the routing software can only route between locations, latitudes and longitudes, the software needs a way to look up the coordinate of an address. This process is known as geocoding, looking up the latitude and longitude from an address string.

Likewise, it is common to put a waypoint on the map, and let the system look up the address of the waypoint. This is known as reverse geocoding, mapping a geographic location to an address string.

Although crucial to routing, Leaflet Routing Machine does not come with a builtin geocoder or reverse geocoder. There are quite a few geocoding services available, and instead of Leaflet Routing Machine choosing one for you, it lets you connect to the geocoding service of your choice. Luckily, adding geocoding is easy.

Adding a geocoding service
In Leaflet Routing Machine, geocoders work as a form of plugin. Geocoders must be written to conform with the interface used by Leaflet Control Geocoder (from the same author as Leaflet Routing Machine). This means that by simply including the file Control.Geocoder.js, it will be possible to use these geocoding services

Nominatim
Bing Locations API
Google Geocoding API
MapQuest Geocoding API
Once you have a geocoding service loaded, you need to tell Leaflet Routing Machine to use it. This is done by adding the option geocoder to the control’s options, specifying the geocoder instance to use:

L.Routing.control({
    waypoints: [
        L.latLng(57.74, 11.94),
        L.latLng(57.6792, 11.949)
    ],
    routeWhileDragging: true,
    geocoder: L.Control.Geocoder.nominatim()
}).addTo(map); 
See Leaflet Control Geocoder’s API for more information about the classes used, and their options.

Adding a geocoder will change the way the control works in two major ways:

1) Input fields for the waypoints’ addresses will be added to the control’s panel 2) Moving a waypoint by dragging it in the map, for example, will automatically look up the address of the new location and update the address field

This is an example of what it looks like:

+
−
Start
End

Hjalmar Brantingsgatan, Linnégatan
9.3 km, 15 min
Head northeast	25 m
Turn left onto Bäcktuvevägen	150 m
Turn right to stay on Bäcktuvevägen	15 m
Enter the traffic circle and take the 1st exit onto Tuvevägen (O 570)	10 m
Exit the traffic circle onto Tuvevägen (O 570)	800 m
Enter the traffic circle and take the 2nd exit onto Tuvevägen	25 m
Exit the traffic circle onto Tuvevägen	250 m
Enter the traffic circle and take the 2nd exit onto Tuvevägen	35 m
Exit the traffic circle onto Tuvevägen	250 m
Enter the traffic circle and take the 1st exit onto Tuvevägen	25 m
Exit the traffic circle onto Tuvevägen	300 m
Enter the traffic circle and take the 2nd exit onto Gustaf Dalénsgatan	30 m
Exit the traffic circle onto Gustaf Dalénsgatan	800 m
Turn left towards Centrum	600 m
Keep right onto Hjalmar Brantingsgatan	200 m
Make a slight right to stay on Hjalmar Brantingsgatan	20 m
Keep left onto Hjalmar Brantingsgatan	450 m
Continue onto Hisingsbron	700 m
Turn right onto Norra Sjöfarten	150 m
Take the ramp on the left towards 158: Sahlgrenska	150 m
Merge left onto Götatunneln (E 45)	1.5 km
Take the ramp towards Centrum Syd	200 m
Turn left onto Järnvågsgatan	150 m
Keep right onto Järnvågsgatan	150 m
Continue onto Linnégatan	1 km
Continue onto Linnéplatsen	150 m
Enter Linnéplatsen and take the 1st exit onto Dag Hammarskjöldsleden	10 m
Exit the traffic circle onto Dag Hammarskjöldsleden	900 m
Make a slight right towards Högsbohöjd	300 m
Turn left onto Margretebergsgatan	50 m
Continue onto Storängsgatan	200 m
Turn right onto Fyradalersgatan	8 m
Turn left onto Storängsgatan	35 m
You have arrived at your destination	0 m
 Leaflet | Maps and routes from OpenStreetMap. data uses ODbL license
Autocomplete
Leaflet Routing Machine supports autocomplete (or type ahead, as it’s sometimes called), meaning it can try to suggest addresses as the user types in an address field. To use this feature, the underlying geocoder service must support it. Support is added by giving the geocoder a method called suggest, which takes the same arguments as the geocode method.

Note that the perhaps most commonly used geocoder, Nominatim, does not have autocomplete, since its usage policy explicitly forbids it.

Below is an example of autocomplete/type ahead, with Mapbox’s geocoding service (currently works best in the U.S.). Go ahead, select one of the addresses and start typing. When you pause for a bit, suggestions based on what you’ve typed so far will appear.

+
−
Start
End

San Francisco – Oakland Bay Bridge, Eastshore Freeway
55.0 km, 49 min
Head northwest on Shrader Street	200 m
Turn right onto 17th Street	450 m
Turn left onto Roosevelt Way	1 km
Turn right onto 14th Street	700 m
Turn left onto Market Street	150 m
Keep left onto Market Street	150 m
Turn right onto Duboce Avenue	550 m
Continue onto 13th Street	800 m
Turn left onto Bryant Street	550 m
Take the ramp on the left towards I 80 East: Oakland	450 m
Merge right onto James Lick Freeway (I 80)	1.5 km
Continue onto San Francisco – Oakland Bay Bridge (I 80)	15 km
Keep left towards I 80 East: Vallejo	10 km
Continue onto Eastshore Freeway (I 80)	5 km
Continue onto Linus F. Claeys Freeway (I 80)	5.5 km
Continue onto Eastshore Freeway (I 80)	500 m
Continue onto Carquinez Bridge (I 80)	2.5 km
Keep left onto I 80	4 km
Take exit 31B towards Tennessee Street	200 m
Keep right towards Tennessee Street East	150 m
Turn left onto Humboldt Street	55 m
Turn right onto Tennessee Street	300 m
Turn left onto Vervais Avenue	1 km
Turn right onto Skyline Drive	150 m
Turn left onto Goheen Circle	150 m
You have arrived at your destination, on the left	0 m
 Leaflet | Maps and routes from OpenStreetMap. Geocoding by OpenRouteServicedata uses ODbL license
Unknown addresses
As mentioned above, a reverse geocoding will be made every time a waypoint’s location changes, to reflect its new address. But what happens if there is no address for the location? This typically happens the waypoint is placed outside inhabited areas, like in the woods, mountains or similar.

For these cases, Leaflet Routing Machine has a fallback that generates a waypoint name. By default, a representation of its latitude and longitude will be used, like “N38.1086, W122.1762”.

If you want to override this behaviour, you can provide the option waypointNameFallback, which is a function that given the waypoint L.LatLng should return a name. Here’s an example of how to replace the default with sexagesimal format of the location:

L.Routing.control({
    [...]
    waypointNameFallback: function(latLng) {
        function zeroPad(n) {
            n = Math.round(n);
            return n < 10 ? '0' + n : n;
        }
        function sexagesimal(p, pos, neg) {
            var n = Math.abs(p),
                degs = Math.floor(n),
                mins = (n - degs) * 60,
                secs = (mins - Math.floor(mins)) * 60,
                frac = Math.round((secs - Math.floor(secs)) * 100);
            return (n >= 0 ? pos : neg) + degs + '°' +
                zeroPad(mins) + '\'' +
                zeroPad(secs) + '.' + zeroPad(frac) + '"';
        }

        return sexagesimal(latLng.lat, 'N', 'S') + ' ' + sexagesimal(latLng.lng, 'E', 'W');
    }
})
Implementing your own geocoder
For some cases, you might want to use a geocoding service that is not supported by Leaflet Control Geocoder. This can be done easily by implementing the same interface (contract) that for your service. IGeocoder lists which methods you need to implement; optionally you might want to add suggest as well, as mentioned under the Autocomplete heading above.

Then simply pass your own geocoder instance to the geocoder option, just like the examples above.

Copyright © 2015 Per Liedman, released under ISC L


First, we need to add a popup when the map is clicked. This code is nothing specific for Leaflet Routing Machine, but rather an example of how you can build basic user interfaces with Leaflet’s builtin functionality, without the use of for example jQuery or similar.

function createButton(label, container) {
    var btn = L.DomUtil.create('button', '', container);
    btn.setAttribute('type', 'button');
    btn.innerHTML = label;
    return btn;
}

map.on('click', function(e) {
    var container = L.DomUtil.create('div'),
        startBtn = createButton('Start from this location', container),
        destBtn = createButton('Go to this location', container);

    L.popup()
        .setContent(container)
        .setLatLng(e.latlng)
        .openOn(map);
});
Adding this should give you a popup once the map is clicked.

Now we need to make something happen when the buttons are clicked. This is the part where we actually interact with Leaflet Routing Machine’s control. This code assumes the routing control instance is stored in control.

When the “Start from this location” button is clicked, the first waypoint of the route should be replaced with the location that the user clicked on. Modifying the waypoints can be done with the method spliceWaypoints, which mimics the behavior of JavaScript’s own Array.splice: with it, you can both add and remove waypoints, even in one operation.

To replace the first waypoint, you simply tell Leaflet Routing Machine to remove one waypoint at index 0 (the first), and then add a new at the clicked location. Add this code inside the map’s click event handler; e will still refer to the click event, and e.latlng is the location clicked:

    L.DomEvent.on(startBtn, 'click', function() {
        control.spliceWaypoints(0, 1, e.latlng);
        map1.closePopup();
    });
 
Similarily, setting the destination is a matter of removing the last waypoint (remember there can be more than two, if there are via points) and adding a new at the clicked location:

    L.DomEvent.on(destBtn, 'click', function() {
        control.spliceWaypoints(control.getWaypoints().length - 1, 1, e.latlng);
        map1.closePopup();
    });
 
As can be seen in the code above, the current waypoints can also be accessed with the getWaypoints method.

Reversing the route
Note: From version 2.3.0 and later, this functionality is included in the plugin by setting the option reverseWaypoints to true for the L.Routing.Plan instance. The code below still serves as a good example of how to add custom buttons to the routing control’s panel.

It is common to have button to reverse the direction of the route (i.e. reverse the list of waypoints). Lets go through the steps necessary to implement such a button.

First, where should we put the button? One suggestion would be to put it next to the button that adds a waypoint to the route, below the last address input field. But how do you add something to that panel, since it is created by Leaflet Routing Machine? We will use pattern that is common when you want to customize parts of the control’s user interface: we will extend the implementing class and override the method that is responsible for creating the UI.

In this case, we need to override the control’s L.Routing.Plan, since its method createGeocoders is what creates the panel we’re going to add a button to.

var ReversablePlan = L.Routing.Plan.extend({
    createGeocoders: function() {
        var container = L.Routing.Plan.prototype.createGeocoders.call(this),
            reverseButton = createButton('↑↓', container);
        return container;
    }
}
We’re creating a new class, ReversablePlan, that inherits from L.Routing.Plan, with one single overridden method, createGeocoders. We’re using our utility method createButton from the example above to create the button. Also note how we call the base implementation of createGeocoders, and simply add the new button to the panel returned by that method, before returning the panel.

Having added the new button, we simply need to attach a listener to it, and make it reverse the route. We add this code inside the createGeocders method, before returning the container:

class=”<pre><code language-javascript”> L.DomEvent.on(reverseButton, ‘click’, function() { var waypoints = this.getWaypoints(); this.setWaypoints(waypoints.reverse()); }, this); </code></pre>

We get the current waypoints with getWaypoints, which returns an array. We then use JavaScript’s builtin method reverse to flip the order of the array, and finally set the waypoints to the reversed array with setWaypoints. Simple, right?

The more observant readers will note that this in the code will be the instance of the ReversablePlan, not the routing control itself, but in the first example getWaypoints was a method on the control, now it appears to be a method on the plan - what is going on? The truth is that while the control has getWaypoints, setWaypoints as well as spliceWaypoints, they are really just shortcuts that call the control’s plan’s methods with the same names. It is the plan’s responsibility to hold the list of waypoints, and the control will query it when needed.

Ok, we now have a ReversablePlan, but how do we use it in the routing control? This is done with yet another option when creating the control:

var plan = new ReversablePlan([
        L.latLng(57.74, 11.94),
        L.latLng(57.6792, 11.949)
    ], {
        geocoder: L.Control.Geocoder.nominatim(),
        routeWhileDragging: true
    }),
    control = L.Routing.control({
        routeWhileDragging: true,
        plan: plan
    }).addTo(map1);
While this looks pretty straight forward, there are a couple of points to note here:

We no longer use the waypoints option to set the initial waypoints, but rather pass them as the first argument when creating the plan instance
The option geocoder is passed when creating the plan instance instance rather than when creating the control
The option routeWhileDragging is passed both when creating the plan as well as when creating the control
Why is this? Well, unless the plan option is specified, the control will instantiate its own plan instance, and when doing so it will pass the same options that were passed to it, meaning the plan will get the same options object that we passed when creating the control. This means that even though the control itself actually doesn’t have a geocoder option, you can pass it one, since the plan does have a geocoder option, and will get the value we passed in to the control. On the other hand, when we use a plan we created ourselves, the plan’s options are already set, since it’s already created, and we need to pass the options directly to the plan when creating it.

The waypoints option is a shortcut that sets the plan’s waypoints, but it can also be achieved by passing the waypoints when creating the plan, so we do that instead.

For full details on available options, methods and events, you can always look up the Leaflet Routing Machine API docs.




result from osrm 